/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { NextFunction, Request, Response } from "express";
import { TypeORMError } from "typeorm";

import { APIError, PGError } from "../errors";
import { errors } from "..";
import { errorCounter } from "./metrics.middleware";
import Logger from "../metrics/logger";
import { unauthorizedErrorLog } from "../metrics/metrics";
import { getCurrentLanguage } from "../utils/language";
import { LANGUAGE } from "../../base/types/typing";

interface CustomRequest extends Request {
    route: {
        path: string;
    };
}

// Helper function to format error response based on current language
function formatErrorResponse(errors: Array<{ message: string; messageFa?: string; field?: string }>) {
    const currentLanguage = getCurrentLanguage();

    return errors.map(error => {
        // If it's English, use the message property
        if (currentLanguage === LANGUAGE.ENGLISH) {
            return {
                message: error.message,
                field: error.field
            };
        } else {
            // If it's Persian (or any other language), use messageFa if available, otherwise fallback to message
            return {
                message: error.messageFa || error.message,
                field: error.field
            };
        }
    });
}

export function errorHandler(
    err: unknown,
    req: CustomRequest,
    res: Response,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    next: NextFunction,
) {
    console.error("Caught error:", err);

    const routePath =
        req.route !== undefined && req.route.path !== undefined
            ? req.route.path
            : req.path;

    try {
        if (err instanceof APIError) {
            if (err instanceof errors.UnauthorizedError) {
                Logger.error("Unauthorize attempt accrued", {
                    action: "UnauthorizedError",
                });
                unauthorizedErrorLog.inc();
            }
            errorCounter.inc({
                method: req.method,
                statusCode: err.statusCode.toString(),
                route: routePath,
            });
            return res
                .status(err.statusCode)
                .send({ errors: formatErrorResponse(err.serializeErrors()) });
        } else if (err instanceof TypeORMError) {
            const pgError = new PGError(err);
            errorCounter.inc({
                method: req.method,
                statusCode: pgError.statusCode.toString(),
                route: routePath,
            });
            return res
                .status(pgError.statusCode)
                .send({ errors: formatErrorResponse(pgError.serializeErrors()) });
        } else {
            errorCounter.inc({
                method: req.method,
                statusCode: "500",
                route: routePath,
            });
            // Instead of throwing a generic error, return a localized InternalError
            const internalError = new errors.InternalError();
            return res.status(internalError.statusCode).send({
                errors: formatErrorResponse(internalError.serializeErrors()),
            });
        }
    } catch {
        const internalError = new errors.InternalError();
        return res.status(internalError.statusCode).send({
            errors: formatErrorResponse(internalError.serializeErrors()),
        });
    }
}
