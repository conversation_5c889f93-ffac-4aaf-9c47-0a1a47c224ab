export declare global {
    namespace NodeJS {
        interface ProcessEnv {
            ENV_PATH: string;
            NODE_ENV: string;
            PROJECT: string;
            DOMAIN: string;
            PORT: string;
            WEB_APP_URL: string;
            POSTGRES_DB_HOST: string;
            POSTGRES_DB_PORT: string;
            POSTGRES_DB_NAME: string;
            POSTGRES_DB_USER: string;
            POSTGRES_DB_PASSWORD: string;
            REDIS_HOST: string;
            REDIS_PORT: string;
            REDIS_PREFIX: string;
            ELASTIC_URI: string;
            SALT_ROUNDS: string;
            SESSION_SECRET: string;
            JWT_SECRET: string;
            KAVENEGAR_API_KEY: string;
            INSTAGRAM_CLIENT_SECRET: string;
            INSTAGRAM_CLIENT_ID: string;
            INSTAGRAM_CALLBACK_URL: string;
            INSTAGRAM_WEBHOOK_URL: string;
            INSTAGRAM_WEBHOOK_TOKEN: string;
            GOOGLE_CLIENT_ID: string;
            GOOGLE_CLIENT_SECRET: string;
            GOOGLE_USERINFO_URL: string;
            OPENAI_API_KEY: string;
            NAVASAN_KEY: string;
            FILER_URL: string;
            ZIBAL_MERCHANT_ID: string;
            ZIBAL_PREFIX_URL: string;
            ZIBAL_CALL_BACK_URL: string;
            PAYMENT_LINK_PREFIX_URL: string;
            ZIBAL_KEY: string;
            WEB_APP_URL: string;
            ENABLE_CONSOLE_LOG: string;
            LOG_FILE_PATH: string;
            ENABLE_PROMETHEUS_LOG: string;
            NAJVA_TOKEN: string;
            NAJVA_API_KEY: string;
            CHATBOT: string;
            EMBEDDING: string;
            VIRTUAL_HOST: string;
            LETSENCRYPT_HOST: string;
            VIRTUAL_PORT: string;
            STATIC_SERVER_URL: string;
            FIX_USER: string;
            FIX_PASS: string;
            PUBLIC_APP_LANG: string;
            LANGFUSE_HOST_URL: string;
            LANGFUSE_SECRET_KEY: string;
            LANGFUSE_PUBLIC_KEY: string;
        }
    }
    export type Nullable<T> = {
        [P in keyof T]: T[P] | null;
    };

    export type StringWithAutocomplete<T> = T | (string & Record<never, never>);
}
