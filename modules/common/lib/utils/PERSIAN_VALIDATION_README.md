# Persian Text Validation System

This system provides comprehensive validation and normalization for Persian/Arabic text input in your application. It automatically handles common issues with Persian text including directional marks, non-joiner spaces, and character normalization.

## Features

- ✅ **Persian/Arabic number normalization** (۱۲۳ → 123)
- ✅ **Character normalization** (ي → ی, ك → ک)
- ✅ **Directional mark removal** (LTR/RTL marks)
- ✅ **Non-joiner space cleanup**
- ✅ **Express middleware integration**
- ✅ **Decorator support**
- ✅ **Error handling and validation**
- ✅ **Nested object support**

## Quick Start

### 1. Basic Text Validation

```typescript
import { validatePersianText } from '../utils/data-normalizer';

const input = "سلام ۱۲۳ ‌test‌ ي ك";
const normalized = validatePersianText(input);
console.log(normalized); // "سلام 123 test ی ک"
```

### 2. Express Middleware (Recommended)

```typescript
import { persianValidationMiddleware } from '../middlewares/persian-validation';

// Apply to all routes
app.use(persianValidationMiddleware({
    validateAllStrings: true,
    throwOnError: false
}));

// Apply to specific routes
app.post('/products', 
    persianValidationMiddleware({
        fields: ['name', 'description'],
        throwOnError: true,
        errorMessage: 'نام یا توضیحات محصول نامعتبر است'
    }),
    (req, res) => {
        // req.body is automatically validated and normalized
        res.json({ success: true, data: req.body });
    }
);
```

### 3. Controller Decorator

```typescript
import { ValidatePersianText } from '../middlewares/persian-validation';

class ProductController {
    @ValidatePersianText({
        fields: ['name', 'description'],
        throwOnError: true
    })
    createProduct(req, res, next) {
        // req.body.name and req.body.description are automatically validated
        // Handle the request...
    }
}
```

## Configuration Options

### PersianValidationConfig

```typescript
interface PersianValidationConfig {
    fields?: string[];              // Specific fields to validate
    validateAllStrings?: boolean;   // Validate all string fields (default: true)
    throwOnError?: boolean;         // Throw errors on validation failure (default: false)
    errorMessage?: string;          // Custom error message
    validateBody?: boolean;         // Validate request body (default: true)
    validateQuery?: boolean;        // Validate query parameters (default: true)
    validateParams?: boolean;       // Validate route parameters (default: false)
}
```

## Usage Examples

### Example 1: Product Creation API

```typescript
// Validate product data
app.post('/api/products', 
    persianValidationMiddleware({
        fields: ['name', 'description', 'category'],
        throwOnError: true,
        errorMessage: 'اطلاعات محصول شامل کاراکترهای نامعتبر است'
    }),
    async (req, res) => {
        try {
            const product = await createProduct(req.body);
            res.json({ success: true, product });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    }
);
```

### Example 2: User Profile Update

```typescript
// Normalize all string fields without throwing errors
app.put('/api/profile', 
    persianValidationMiddleware({
        validateAllStrings: true,
        throwOnError: false
    }),
    async (req, res) => {
        // All string fields in req.body are automatically normalized
        const updatedProfile = await updateUserProfile(req.user.id, req.body);
        res.json({ success: true, profile: updatedProfile });
    }
);
```

### Example 3: Search with Query Validation

```typescript
// Validate search queries
app.get('/api/search', 
    persianValidationMiddleware({
        validateQuery: true,
        validateBody: false,
        fields: ['q', 'category'],
        throwOnError: false
    }),
    async (req, res) => {
        const results = await searchProducts(req.query);
        res.json({ results });
    }
);
```

### Example 4: Manual Validation

```typescript
import { validateSinglePersianText } from '../middlewares/persian-validation';

function processUserInput(input: string) {
    try {
        const validated = validateSinglePersianText(input, true);
        return { success: true, data: validated };
    } catch (error) {
        return { success: false, error: error.message };
    }
}
```

### Example 5: Bulk Data Normalization

```typescript
import { normalizeData } from '../utils/data-normalizer';

const userData = {
    name: "علی ۱۲۳ ‌احمدی‌",
    phone: "۰۹۱۲۳۴۵۶۷۸۹",
    address: {
        street: "خیابان ولیعصر ۱۲۳",
        city: "تهران"
    }
};

const normalized = normalizeData(userData, {
    type: "object",
    fieldOptions: {
        name: { type: "text" },
        phone: { type: "text" }
    }
});
```

## Error Handling

When `throwOnError` is enabled, the middleware will return a 400 status with detailed error information:

```json
{
    "error": "Validation Error",
    "message": "نام محصول شامل کاراکترهای نامعتبر است",
    "field": "name",
    "originalValue": "‌‌‌نام نامعتبر‌‌‌"
}
```

## Integration with Existing Code

### Adding to Existing Routes

```typescript
// Before
app.post('/api/products', createProduct);

// After
app.post('/api/products', 
    persianValidationMiddleware({ 
        fields: ['name', 'description'],
        throwOnError: true 
    }),
    createProduct
);
```

### Global Application

```typescript
// Apply to all routes in your app.ts
app.use(persianValidationMiddleware({
    validateAllStrings: true,
    throwOnError: false
}));
```

## Best Practices

1. **Use middleware for API endpoints** that accept Persian text
2. **Enable error throwing** for critical fields like product names
3. **Validate query parameters** for search functionality
4. **Apply globally** for consistent text normalization
5. **Test thoroughly** with various Persian text inputs

## Testing

Run the included tests to verify functionality:

```bash
npm test -- persian-validation.test.ts
```

## What Gets Normalized

- **Persian digits**: ۰۱۲۳۴۵۶۷۸۹ → 0123456789
- **Arabic digits**: ٠١٢٣٤٥٦٧٨٩ → 0123456789
- **Arabic characters**: ي → ی, ك → ک
- **Directional marks**: \u200e, \u200f (removed)
- **Non-joiner spaces**: ‌ (removed from start/end)
- **Hidden characters**: Various RTL/LTR override characters

This system ensures consistent, clean Persian text throughout your application while maintaining compatibility with existing code.
