import { validatePersianText, normalizeData } from '../data-normalizer';
import { 
    persianValidationMiddleware, 
    validateSinglePersianText,
    PersianValidationError 
} from '../../middlewares/persian-validation';

describe('Persian Text Validation', () => {
    describe('validatePersianText', () => {
        it('should normalize Persian numbers to English', () => {
            const input = '۱۲۳۴۵۶۷۸۹۰';
            const expected = '1234567890';
            expect(validatePersianText(input)).toBe(expected);
        });

        it('should normalize Arabic numbers to English', () => {
            const input = '٠١٢٣٤٥٦٧٨٩';
            const expected = '0123456789';
            expect(validatePersianText(input)).toBe(expected);
        });

        it('should normalize Arabic characters to Persian', () => {
            const input = 'سلام ي ك';
            const expected = 'سلام ی ک';
            expect(validatePersianText(input)).toBe(expected);
        });

        it('should remove directional marks', () => {
            const input = 'سلام\u200etest\u200f';
            const expected = 'سلامtest';
            expect(validatePersianText(input)).toBe(expected);
        });

        it('should remove Persian non-joiner spaces from start and end', () => {
            const input = '‌سلام‌';
            const expected = 'سلام';
            expect(validatePersianText(input)).toBe(expected);
        });

        it('should handle complex mixed text', () => {
            const input = '‌نام: علی ۱۲۳ ي ك‌';
            const expected = 'نام: علی 123 ی ک';
            expect(validatePersianText(input)).toBe(expected);
        });

        it('should handle null and undefined inputs', () => {
            expect(validatePersianText(null)).toBe('');
            expect(validatePersianText(undefined)).toBe('');
            expect(validatePersianText('')).toBe('');
        });

        it('should handle number inputs', () => {
            expect(validatePersianText(123)).toBe('123');
        });
    });

    describe('normalizeData', () => {
        it('should normalize text type data', () => {
            const input = 'سلام ۱۲۳ ي';
            const result = normalizeData(input, { type: 'text' });
            expect(result).toBe('سلام 123 ی');
        });

        it('should normalize object with Persian text', () => {
            const input = {
                name: 'علی ۱۲۳ ي',
                age: 25,
                city: 'تهران ۱۲۳'
            };
            
            const result = normalizeData(input, {
                type: 'object',
                fieldOptions: {
                    name: { type: 'text' },
                    city: { type: 'text' }
                }
            });

            expect(result.name).toBe('علی 123 ی');
            expect(result.city).toBe('تهران 123');
            expect(result.age).toBe(25);
        });

        it('should normalize nested objects', () => {
            const input = {
                user: {
                    name: 'علی ۱۲۳ ي',
                    address: {
                        street: 'خیابان ولیعصر ۱۲۳'
                    }
                }
            };

            const result = normalizeData(input, { type: 'object' });
            expect(result.user.name).toBe('علی 123 ی');
            expect(result.user.address.street).toBe('خیابان ولیعصر 123');
        });

        it('should normalize arrays', () => {
            const input = ['سلام ۱۲۳', 'خداحافظ ۴۵۶'];
            const result = normalizeData(input, { type: 'array' });
            expect(result[0]).toBe('سلام 123');
            expect(result[1]).toBe('خداحافظ 456');
        });
    });

    describe('validateSinglePersianText', () => {
        it('should validate and normalize text without throwing errors', () => {
            const input = 'سلام ۱۲۳ ي';
            const result = validateSinglePersianText(input, false);
            expect(result).toBe('سلام 123 ی');
        });

        it('should throw error when throwOnError is true and significant changes occur', () => {
            // This test might need adjustment based on what constitutes "significant changes"
            const input = '‌‌‌‌‌‌‌‌‌‌test‌‌‌‌‌‌‌‌‌‌'; // Many non-joiner spaces
            expect(() => validateSinglePersianText(input, true)).toThrow(PersianValidationError);
        });
    });

    describe('persianValidationMiddleware', () => {
        let req: any;
        let res: any;
        let next: jest.Mock;

        beforeEach(() => {
            req = {
                body: {},
                query: {},
                params: {}
            };
            res = {
                status: jest.fn().mockReturnThis(),
                json: jest.fn()
            };
            next = jest.fn();
        });

        it('should normalize request body', () => {
            req.body = {
                name: 'علی ۱۲۳ ي',
                description: 'توضیحات ۴۵۶'
            };

            const middleware = persianValidationMiddleware({
                validateAllStrings: true,
                throwOnError: false
            });

            middleware(req, res, next);

            expect(req.body.name).toBe('علی 123 ی');
            expect(req.body.description).toBe('توضیحات 456');
            expect(next).toHaveBeenCalledWith();
        });

        it('should normalize query parameters', () => {
            req.query = {
                search: 'جستجو ۱۲۳ ي'
            };

            const middleware = persianValidationMiddleware({
                validateAllStrings: true,
                throwOnError: false
            });

            middleware(req, res, next);

            expect(req.query.search).toBe('جستجو 123 ی');
            expect(next).toHaveBeenCalledWith();
        });

        it('should validate specific fields only', () => {
            req.body = {
                name: 'علی ۱۲۳ ي',
                email: '<EMAIL>',
                description: 'توضیحات ۴۵۶'
            };

            const middleware = persianValidationMiddleware({
                fields: ['name', 'description'],
                validateAllStrings: false,
                throwOnError: false
            });

            middleware(req, res, next);

            expect(req.body.name).toBe('علی 123 ی');
            expect(req.body.description).toBe('توضیحات 456');
            expect(req.body.email).toBe('<EMAIL>'); // Should remain unchanged
            expect(next).toHaveBeenCalledWith();
        });

        it('should handle validation errors when throwOnError is true', () => {
            req.body = {
                name: '‌‌‌‌‌‌‌‌‌‌test‌‌‌‌‌‌‌‌‌‌' // Many non-joiner spaces
            };

            const middleware = persianValidationMiddleware({
                fields: ['name'],
                throwOnError: true,
                errorMessage: 'نام نامعتبر است'
            });

            middleware(req, res, next);

            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    error: 'Validation Error',
                    field: 'name'
                })
            );
            expect(next).not.toHaveBeenCalled();
        });

        it('should handle nested objects', () => {
            req.body = {
                user: {
                    name: 'علی ۱۲۳ ي',
                    profile: {
                        bio: 'بیوگرافی ۴۵۶'
                    }
                }
            };

            const middleware = persianValidationMiddleware({
                validateAllStrings: true,
                throwOnError: false
            });

            middleware(req, res, next);

            expect(req.body.user.name).toBe('علی 123 ی');
            expect(req.body.user.profile.bio).toBe('بیوگرافی 456');
            expect(next).toHaveBeenCalledWith();
        });
    });
});
