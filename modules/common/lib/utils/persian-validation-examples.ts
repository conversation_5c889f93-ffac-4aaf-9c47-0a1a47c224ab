/**
 * Examples and usage patterns for Persian text validation
 * This file demonstrates how to use the Persian validation utilities
 */

import { validatePersianText, normalizeData } from './data-normalizer';
import { 
    persianValidationMiddleware, 
    ValidatePersianText, 
    validateSinglePersianText,
    PersianValidationConfig 
} from '../middlewares/persian-validation';

/**
 * Example 1: Basic Persian text validation
 */
export function basicValidationExample() {
    const input = "سلام ۱۲۳ ‌test‌ ي ك";
    const normalized = validatePersianText(input);
    console.log('Original:', input);
    console.log('Normalized:', normalized); // "سلام 123 test ی ک"
    
    return normalized;
}

/**
 * Example 2: Using the validation middleware in Express routes
 */
export function middlewareExample() {
    // Example middleware configuration for validating specific fields
    const validationConfig: PersianValidationConfig = {
        fields: ['name', 'description', 'address'],
        validateAllStrings: false,
        throwOnError: true,
        errorMessage: 'نام یا توضیحات شامل کاراکترهای نامعتبر است',
        validateBody: true,
        validateQuery: true,
        validateParams: false
    };
    
    return persianValidationMiddleware(validationConfig);
}

/**
 * Example 3: Using the decorator for automatic validation
 */
export class ExampleController {
    @ValidatePersianText({
        fields: ['title', 'content'],
        throwOnError: true,
        errorMessage: 'عنوان یا محتوا شامل کاراکترهای نامعتبر است'
    })
    createPost(req: any, res: any, next: any) {
        // req.body.title and req.body.content are automatically validated and normalized
        console.log('Validated data:', req.body);
        res.json({ success: true, data: req.body });
    }
    
    @ValidatePersianText({
        validateAllStrings: true,
        throwOnError: false // Just normalize, don't throw errors
    })
    updateProfile(req: any, res: any, next: any) {
        // All string fields in req.body are automatically normalized
        console.log('Normalized profile data:', req.body);
        res.json({ success: true, data: req.body });
    }
}

/**
 * Example 4: Manual validation with error handling
 */
export function manualValidationExample() {
    const userInput = "نام کاربر: ۱۲۳ ‌test‌ ي";
    
    try {
        // Validate with error throwing enabled
        const validated = validateSinglePersianText(userInput, true);
        console.log('Validation successful:', validated);
        return { success: true, data: validated };
    } catch (error) {
        console.error('Validation failed:', error.message);
        return { success: false, error: error.message };
    }
}

/**
 * Example 5: Bulk data normalization
 */
export function bulkDataNormalizationExample() {
    const userData = {
        name: "علی ۱۲۳ ‌احمدی‌",
        email: "<EMAIL>",
        phone: "۰۹۱۲۳۴۵۶۷۸۹",
        address: {
            street: "خیابان ولیعصر ۱۲۳",
            city: "تهران",
            postalCode: "۱۲۳۴۵۶۷۸۹۰"
        },
        preferences: {
            language: "fa",
            notifications: true
        }
    };
    
    // Normalize the entire object
    const normalizedData = normalizeData(userData, {
        type: "object",
        fieldOptions: {
            name: { type: "text" },
            phone: { type: "text" },
            address: { type: "object" }
        }
    });
    
    console.log('Original data:', userData);
    console.log('Normalized data:', normalizedData);
    
    return normalizedData;
}

/**
 * Example 6: Express route with middleware
 */
export function expressRouteExample() {
    // This is how you would use it in an actual Express route
    const express = require('express');
    const router = express.Router();
    
    // Apply Persian validation to all routes in this router
    router.use(persianValidationMiddleware({
        validateAllStrings: true,
        throwOnError: false,
        errorMessage: 'داده‌های ورودی شامل کاراکترهای نامعتبر است'
    }));
    
    // Apply specific validation to a particular route
    router.post('/products', 
        persianValidationMiddleware({
            fields: ['name', 'description', 'category'],
            throwOnError: true,
            errorMessage: 'نام، توضیحات یا دسته‌بندی محصول نامعتبر است'
        }),
        (req: any, res: any) => {
            // req.body is automatically validated and normalized
            res.json({ success: true, product: req.body });
        }
    );
    
    return router;
}

/**
 * Example 7: Custom validation for specific use cases
 */
export function customValidationExample() {
    // Example: Validate product names
    function validateProductName(name: string): string {
        const normalized = validatePersianText(name);
        
        // Additional custom validation
        if (normalized.length < 2) {
            throw new Error('نام محصول باید حداقل ۲ کاراکتر باشد');
        }
        
        if (normalized.length > 100) {
            throw new Error('نام محصول نباید بیش از ۱۰۰ کاراکتر باشد');
        }
        
        return normalized;
    }
    
    // Example: Validate Persian phone numbers
    function validatePersianPhoneNumber(phone: string): string {
        const normalized = validatePersianText(phone);
        
        // Remove any non-digit characters
        const digitsOnly = normalized.replace(/\D/g, '');
        
        // Validate Iranian phone number format
        if (!/^09\d{9}$/.test(digitsOnly)) {
            throw new Error('شماره تلفن باید با ۰۹ شروع شده و ۱۱ رقم باشد');
        }
        
        return digitsOnly;
    }
    
    return {
        validateProductName,
        validatePersianPhoneNumber
    };
}

/**
 * Example 8: Integration with existing validation libraries (like Joi or Yup)
 */
export function integrationExample() {
    // Example with a hypothetical validation schema
    const validationSchema = {
        name: {
            type: 'string',
            required: true,
            transform: (value: string) => validatePersianText(value),
            validate: (value: string) => {
                if (value.length < 2) {
                    throw new Error('نام باید حداقل ۲ کاراکتر باشد');
                }
                return true;
            }
        },
        description: {
            type: 'string',
            required: false,
            transform: (value: string) => validatePersianText(value)
        }
    };
    
    return validationSchema;
}
