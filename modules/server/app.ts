import express from "express";
import "express-async-errors";
import morgan from "morgan";
import session from "express-session";
import passport from "passport";

import cors from "cors";

import { auth, middlewares } from "../common";

import router from "./router";
import { metricsMiddleware } from "../common/lib/middlewares/metrics.middleware";
import { PrometheusLogTransformer } from "../common/lib/metrics/transformers/PrometheusLogTransformer";
import { ConsoleLogTransformer } from "../common/lib/metrics/transformers/ConsoleLogTransformer";
import { FileLogTransformer } from "../common/lib/metrics/transformers/FileLogTransformer";
import Logger from "../common/lib/metrics/logger";

const app = express();

auth.initPassportJWT();
auth.initPassportJWTExists();

process.env.ENABLE_CONSOLE_LOG === "true" &&
    Logger.addTransformer(new ConsoleLogTransformer());

process.env.LOG_FILE_PATH
    ? Logger.addTransformer(new FileLogTransformer(process.env.LOG_FILE_PATH))
    : console.error("LOG_FILE_PATH environment variable is not defined.");

process.env.ENABLE_PROMETHEUS_LOG === "true" &&
    Logger.addTransformer(new PrometheusLogTransformer());

app.use(morgan("dev"));
app.use(
    session({
        secret: process.env.SESSION_SECRET,
        resave: false,
        saveUninitialized: true,
    }) as any,
);
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(passport.initialize() as any);
app.use(passport.session());
app.use(cors());
// app.use(
//     cors({
//         origin: [
//             "https://bookish-funicular-r4g54q5w5ppcx5qw-3000.app.github.dev/authentication/",
//             "http://localhost:5000",
//             "https://palette-tech.io/",
//             "https://app.palette-tech.io"
//         ],
//         methods: ["GET", "POST", "PUT", "DELETE","PATCH"],
//         allowedHeaders: ["Content-Type", "Authorization"],
//         credentials: true,
//     }),
// );


app.use(middlewares.queryParser);
app.use(middlewares.responseHandler);
app.use(metricsMiddleware);
app.use("/api/v1", router);
app.use(middlewares.errorHandler);

export default app;

// app.use(camelCase());
// app.use(queryParser());
